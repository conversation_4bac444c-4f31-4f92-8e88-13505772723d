INFO:     Will watch for changes in these directories: ['/Users/<USER>/Documents/augment-projects/PrintMind/backend']
ERROR:    [Errno 48] Address already in use
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       INFO:     127.0.0.1:60115 - "GET /health HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/core/config.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [4616]
INFO:     Started server process [4703]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/core/config.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [4703]
INFO:     Started server process [4711]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [4711]
INFO:     Stopping reloader process [3391]
