
> frontend@0.0.0 dev
> vite

10:23:55 [vite] (client) Re-optimizing dependencies because lockfile has changed
Port 5176 is in use, trying another one...

  VITE v6.3.5  ready in 536 ms

  ➜  Local:   http://localhost:5177/
  ➜  Network: http://************:5177/
  ➜  Network: http://**********:5177/
  ➜  Vue DevTools: Open http://localhost:5177/__devtools__/ as a separate window
  ➜  Vue DevTools: Press Option(⌥)+Shift(⇧)+D in App to toggle the Vue DevTools
